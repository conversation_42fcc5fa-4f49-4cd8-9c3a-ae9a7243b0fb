'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  const navItems = [
    { href: '/', label: 'Home' },
    { href: '/about', label: 'About' },
    { href: '/work', label: 'Work' },
    { href: '/contact', label: 'Contact' }
  ]

  return (
    <header className="w-full py-6 px-6 md:px-12 lg:px-24">
      <div className="max-w-7xl mx-auto">
        <nav className="flex justify-between items-center">
        {/* Logo */}
        <Link href="/" className="flex items-center">
          <Image
            src="/images/logo.png"
            alt="Navhaus"
            width={120}
            height={40}
            className="h-8 w-auto lg:-mt-[15px]"
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`font-medium uppercase tracking-wide transition-colors duration-200 ${
                pathname === item.href
                  ? 'text-bauhaus-red'
                  : 'text-bauhaus-black hover:text-bauhaus-red'
              }`}
            >
              {item.label}
            </Link>
          ))}

          {/* CTA Button */}
          <Link href="/contact" className="btn-primary ml-8">
            Start Project
          </Link>
        </div>

        {/* Mobile menu button */}
        <button
          className="md:hidden text-bauhaus-black"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {isMenuOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
        </nav>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-6 py-6 border-t border-bauhaus-black">
            <div className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`font-medium uppercase tracking-wide transition-colors duration-200 ${
                    pathname === item.href
                      ? 'text-bauhaus-red'
                      : 'text-bauhaus-black hover:text-bauhaus-red'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <Link
                href="/contact"
                className="btn-primary inline-block mt-4"
                onClick={() => setIsMenuOpen(false)}
              >
                Start Project
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
