"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shapes/AnimatedShapes */ \"(app-pages-browser)/./src/components/shapes/AnimatedShapes.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative w-full bg-brand-background text-bauhaus-black overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                    className: \"w-full h-full text-black\",\n                    opacity: \"default\",\n                    animationPreset: \"drift\",\n                    animationIndex: 200\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-5 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    src: \"/images/logo.png\",\n                                                    alt: \"Navhaus\",\n                                                    width: 140,\n                                                    height: 45,\n                                                    className: \"h-10 w-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-2 -right-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"red\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 201\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-bauhaus-black\",\n                                                    children: \"What matters, made real.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed max-w-sm\",\n                                                    children: \"We build bold, efficient, and meaningful digital experiences. Nothing more, nothing less.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/work\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-4 space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-bauhaus-black font-medium\",\n                                                    children: \"Ready to build something?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"inline-block px-4 py-2 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-brand-background transition-colors duration-200 rounded-xl text-sm\",\n                                                    children: \"Start here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -left-4 flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-red rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-yellow rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-blue rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Usually responds within 24 hours\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative py-12 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-px bg-gray-800\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                            width: \"lg\",\n                                            height: \"sm\",\n                                            color: \"red\",\n                                            className: \"w-16 h-4\",\n                                            animationPreset: \"flowing\",\n                                            animationIndex: 205\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                            size: \"md\",\n                                            color: \"yellow\",\n                                            className: \"w-8 h-8\",\n                                            animationPreset: \"pulse\",\n                                            animationIndex: 206\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                            size: \"md\",\n                                            color: \"blue\",\n                                            direction: \"up\",\n                                            className: \"w-8 h-8\",\n                                            animationPreset: \"dynamic\",\n                                            animationIndex: 207\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Navhaus. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Built with intention\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-red rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-yellow rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-blue rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-16 left-16 opacity-15\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedBlob, {\n                    color: \"red\",\n                    className: \"w-24 h-24\",\n                    animationPreset: \"drift\",\n                    animationIndex: 208\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 right-20 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedQuarterCircle, {\n                    color: \"blue\",\n                    corner: \"top-left\",\n                    className: \"w-32 h-32\",\n                    animationPreset: \"gentle\",\n                    animationIndex: 209\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 right-8 opacity-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                    width: \"lg\",\n                    height: \"xl\",\n                    color: \"yellow\",\n                    className: \"w-8 h-24\",\n                    animationPreset: \"float\",\n                    animationIndex: 210\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/4 opacity-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                    size: \"lg\",\n                    color: \"red\",\n                    className: \"w-16 h-16\",\n                    animationPreset: \"energetic\",\n                    animationIndex: 211\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 right-1/3 opacity-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                    size: \"lg\",\n                    color: \"yellow\",\n                    direction: \"up\",\n                    className: \"w-12 h-12\",\n                    animationPreset: \"pulse\",\n                    animationIndex: 212\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Footer.tsx\n"));

/***/ })

});