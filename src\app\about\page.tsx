import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'

export default function About() {
  return (
    <PageWrapper>
      {/* What We Are - Asymmetric Swiss Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Background grid */}
        <div className="absolute inset-0 opacity-20">
          <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
        </div>

        {/* Swiss-style asymmetric grid with rounded elements */}
          <div className="grid grid-cols-12 gap-6 relative z-10">
            {/* Large typography block */}
            <div className="col-span-12 lg:col-span-8">
              <div className="relative">
                <div className="absolute -top-4 -left-4">
                  <AnimatedSoftCircle size="lg" color="red" className="w-16 h-16" animationPreset="gentle" animationIndex={1} />
                </div>
                <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-none mb-8 relative z-10">
                  WHAT<br />
                  WE ARE
                </h1>
              </div>
            </div>

            {/* Soft geometric accent */}
            <div className="col-span-12 lg:col-span-4 flex items-start justify-end">
              <AnimatedRoundedRectangle width="xl" height="xl" color="yellow" className="w-32 h-24" animationPreset="gentle" animationIndex={2} />
            </div>

            {/* Content blocks with rounded corners */}
            <div className="col-span-12 lg:col-span-5">
              <div className="bg-bauhaus-black text-brand-background p-8 rounded-3xl relative overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="default" animationPreset="drift" animationIndex={3} />
                </div>
                <div className="absolute top-4 right-4">
                  <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="gentle" animationIndex={4} />
                </div>
                <p className="text-lg font-medium leading-tight relative z-10">
                  Navhaus is a small, senior team of designers and developers.
                </p>
              </div>
            </div>

            <div className="col-span-12 lg:col-span-2">
              <div className="h-full bg-bauhaus-blue rounded-3xl relative overflow-hidden">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <AnimatedTriangle size="md" color="white" direction="up" className="w-8 h-8" animationPreset="pulse" animationIndex={5} />
                </div>
              </div>
            </div>

            <div className="col-span-12 lg:col-span-5">
              <div className="space-y-6">
                <div className="bg-brand-background border-3 border-bauhaus-black p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute inset-0 opacity-40">
                    <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={6} />
                  </div>
                  <p className="text-lg leading-tight relative z-10">
                    We build custom digital experiences. Fast, focused, and built to last.
                  </p>
                </div>
                <div className="bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute bottom-2 right-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="black" className="w-4 h-4" animationPreset="gentle" animationIndex={7} />
                  </div>
                  <p className="font-bold text-lg relative z-10">
                    No fluff. No bloat. Just what matters, made real.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Soft floating background elements */}
        <div className="absolute top-20 right-20 opacity-30">
          <AnimatedRoundedRectangle width="sm" height="xl" color="blue" className="w-8 h-40" animationPreset="drift" animationIndex={8} />
        </div>
        <div className="absolute bottom-20 left-20 opacity-40">
          <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={9} />
        </div>
      </section>

      {/* What We Believe - Bauhaus Modular Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Swiss typography header with soft accents */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-6">
              <h2 className="text-5xl md:text-6xl font-bold leading-none">
                WHAT WE<br />
                <span className="text-bauhaus-red">BELIEVE</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-6 flex items-end">
              <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-full h-4" animationPreset="gentle" animationIndex={11} />
            </div>
          </div>

          {/* Modular belief blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Belief 1 - Large block with rounded corners */}
            <div className="col-span-12 lg:col-span-8">
              <div className="bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl relative h-full overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="default" animationPreset="drift" animationIndex={12} />
                </div>
                <div className="absolute top-4 left-4">
                  <AnimatedRoundedRectangle width="xl" height="sm" color="red" className="w-16 h-2" animationPreset="gentle" animationIndex={13} />
                </div>
                <h3 className="text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10">
                  Clarity is everything.
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10">
                  <p>Good design doesn't scream. It works.</p>
                  <p>Good code doesn't show off. It ships.</p>
                </div>
              </div>
            </div>

            {/* Yellow accent block with soft corners */}
            <div className="col-span-12 lg:col-span-4">
              <div className="h-full bg-bauhaus-yellow rounded-3xl flex items-center justify-center relative overflow-hidden">
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-20">
                  <AnimatedSoftCircle size="xl" color="black" className="w-20 h-20" animationPreset="gentle" animationIndex={14} />
                </div>
                <div className="text-6xl font-bold text-bauhaus-black relative z-10">01</div>
              </div>
            </div>

            {/* Blue accent block with soft corners */}
            <div className="col-span-12 lg:col-span-3">
              <div className="h-full bg-bauhaus-blue rounded-3xl flex items-center justify-center relative overflow-hidden">
                <div className="absolute bottom-2 right-2">
                  <AnimatedTriangle size="sm" color="white" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={15} />
                </div>
                <div className="text-4xl font-bold text-white relative z-10">02</div>
              </div>
            </div>

            {/* Belief 2 - Medium block with rounded corners */}
            <div className="col-span-12 lg:col-span-9">
              <div className="bg-brand-background border-3 border-bauhaus-black p-8 rounded-3xl relative overflow-hidden">
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={16} />
                </div>
                <div className="absolute top-4 right-4">
                  <AnimatedRoundedRectangle width="sm" height="xl" color="blue" className="w-2 h-16" animationPreset="gentle" animationIndex={17} />
                </div>
                <h3 className="text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10">
                  Small teams move faster.
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10">
                  <p>You work directly with the people doing the work.</p>
                  <p>No account managers. No middle layers. No miscommunication.</p>
                </div>
              </div>
            </div>

            {/* Belief 3 - Full width with soft split */}
            <div className="col-span-12">
              <div className="grid grid-cols-12 gap-6">
                <div className="col-span-12 lg:col-span-2">
                  <div className="h-full bg-bauhaus-red rounded-3xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute top-2 left-2">
                      <AnimatedSoftCircle size="sm" color="white" className="w-4 h-4" animationPreset="gentle" animationIndex={18} />
                    </div>
                    <div className="text-4xl font-bold text-white relative z-10">03</div>
                  </div>
                </div>
                <div className="col-span-12 lg:col-span-10">
                  <div className="bg-bauhaus-yellow text-bauhaus-black p-8 md:p-12 rounded-3xl relative overflow-hidden">
                    <div className="absolute inset-0 opacity-30">
                      <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={19} />
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-8 h-4" animationPreset="gentle" animationIndex={20} />
                    </div>
                    <h3 className="text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10">
                      Function first, aesthetics second.
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10">
                      <p>Beauty is great. Usability is greater.</p>
                      <p>We never choose one at the cost of the other.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Soft floating accent elements */}
        <div className="absolute top-20 right-20 opacity-30">
          <AnimatedRoundedRectangle width="lg" height="xl" color="red" className="w-16 h-80" animationPreset="drift" animationIndex={21} />
        </div>
        <div className="absolute bottom-20 left-20 opacity-40">
          <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="gentle" animationIndex={22} />
        </div>
      </section>

      {/* Why We Exist - Swiss Poster Style */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Large typographic treatment */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-8">
              <h2 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-none">
                WHY WE<br />
                <span className="text-bauhaus-yellow">EXIST</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-4 flex items-end justify-end">
              <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={23} />
            </div>
          </div>

          {/* Swiss-style reason blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Reason 1 - Rounded */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-bauhaus-red text-white p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden">
                <div className="absolute inset-0 opacity-20">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="subtle" animationIndex={24} />
                </div>
                <div className="text-8xl font-bold opacity-20 relative z-10">01</div>
                <p className="text-lg leading-tight relative z-10">
                  Because the internet is full of websites that say too much and do too little.
                </p>
              </div>
            </div>

            {/* Reason 2 - Rounded */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-brand-background text-bauhaus-black border-3 border-bauhaus-yellow p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden">
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={25} />
                </div>
                <div className="absolute top-4 right-4">
                  <AnimatedSoftCircle size="md" color="yellow" className="w-8 h-8" animationPreset="gentle" animationIndex={26} />
                </div>
                <div className="text-8xl font-bold text-bauhaus-yellow opacity-50 relative z-10">02</div>
                <p className="text-lg leading-tight relative z-10">
                  Because bloated codebases and unnecessary meetings waste everyone's time.
                </p>
              </div>
            </div>

            {/* Reason 3 - Rounded */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-bauhaus-blue text-white p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden">
                <div className="absolute inset-0 opacity-20">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={27} />
                </div>
                <div className="absolute bottom-4 left-4">
                  <AnimatedTriangle size="sm" color="white" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={28} />
                </div>
                <div className="text-8xl font-bold opacity-20 relative z-10">03</div>
                <p className="text-lg leading-tight relative z-10">
                  Because you shouldn't have to choose between good design and fast development.
                </p>
              </div>
            </div>

            {/* Full-width conclusion with rounded corners */}
            <div className="col-span-12 mt-8">
              <div className="bg-bauhaus-yellow text-bauhaus-black p-12 text-center rounded-3xl relative overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={29} />
                </div>
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                  <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-16 h-4" animationPreset="gentle" animationIndex={30} />
                </div>
                <p className="text-2xl md:text-3xl font-bold uppercase tracking-wide relative z-10">
                  We're here to cut through the noise and build the right thing, the right way.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Soft floating accent lines */}
        <div className="absolute left-0 top-1/3 opacity-40">
          <AnimatedRoundedRectangle width="sm" height="xl" color="yellow" className="w-8 h-40" animationPreset="drift" animationIndex={31} />
        </div>
        <div className="absolute right-0 bottom-1/3 opacity-30">
          <AnimatedRoundedRectangle width="md" height="xl" color="red" className="w-12 h-60" animationPreset="gentle" animationIndex={32} />
        </div>
      </section>

      {/* How We Work - Constructivist Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Constructivist header */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-6">
              <h2 className="text-5xl md:text-6xl font-bold leading-none">
                HOW WE<br />
                <span className="text-bauhaus-blue">WORK</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-6 flex items-center justify-end">
              <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-32 h-8" animationPreset="gentle" animationIndex={33} />
            </div>
          </div>

          {/* Constructivist process blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Step 1 - Large block with rounded corners */}
            <div className="col-span-12 lg:col-span-6">
              <div className="bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl relative overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="default" animationPreset="drift" animationIndex={34} />
                </div>
                <div className="absolute top-4 left-4">
                  <AnimatedSoftCircle size="lg" color="red" className="w-16 h-16" animationPreset="gentle" animationIndex={35} />
                </div>
                <div className="text-right relative z-10">
                  <div className="text-6xl font-bold text-bauhaus-red mb-4">01</div>
                  <h3 className="text-xl md:text-2xl font-bold uppercase tracking-wide mb-4">
                    We listen first.
                  </h3>
                </div>
              </div>
            </div>

            {/* Step 2 - Vertical block with rounded corners */}
            <div className="col-span-12 lg:col-span-3">
              <div className="bg-bauhaus-yellow text-bauhaus-black p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={36} />
                </div>
                <div className="text-6xl font-bold relative z-10">02</div>
                <h3 className="text-lg font-bold uppercase tracking-wide relative z-10">
                  We distill ideas into clear, minimal interfaces.
                </h3>
              </div>
            </div>

            {/* Step 3 - Square block with rounded corners */}
            <div className="col-span-12 lg:col-span-3">
              <div className="bg-bauhaus-blue text-white p-8 aspect-square rounded-3xl flex flex-col justify-between relative overflow-hidden">
                <div className="absolute inset-0 opacity-20">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={37} />
                </div>
                <div className="absolute bottom-4 right-4">
                  <AnimatedTriangle size="sm" color="white" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={38} />
                </div>
                <div className="text-6xl font-bold relative z-10">03</div>
                <h3 className="text-lg font-bold uppercase tracking-wide relative z-10">
                  We build with clean systems, not hacks.
                </h3>
              </div>
            </div>

            {/* Step 4 - Wide block with rounded corners */}
            <div className="col-span-12">
              <div className="grid grid-cols-12 gap-6">
                <div className="col-span-12 lg:col-span-2">
                  <div className="h-full bg-bauhaus-red rounded-3xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute top-2 left-2">
                      <AnimatedSoftCircle size="sm" color="white" className="w-4 h-4" animationPreset="gentle" animationIndex={39} />
                    </div>
                    <div className="text-6xl font-bold text-white relative z-10">04</div>
                  </div>
                </div>
                <div className="col-span-12 lg:col-span-10">
                  <div className="bg-brand-background border-3 border-bauhaus-black p-8 md:p-12 rounded-3xl flex items-center relative overflow-hidden">
                    <div className="absolute inset-0 opacity-40">
                      <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={40} />
                    </div>
                    <h3 className="text-xl md:text-2xl font-bold uppercase tracking-wide relative z-10">
                      We don't outsource. We don't upsell. We don't hide behind jargon.
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Soft floating accent elements */}
        <div className="absolute top-20 left-20 opacity-30">
          <AnimatedRoundedRectangle width="sm" height="xl" color="red" className="w-4 h-60" animationPreset="drift" animationIndex={41} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-40">
          <AnimatedRoundedRectangle width="xl" height="sm" color="blue" className="w-40 h-4" animationPreset="gentle" animationIndex={42} />
        </div>
      </section>

      {/* CTA Section - Suprematist Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-12 gap-6 items-center">
            {/* Soft geometric composition */}
            <div className="col-span-12 lg:col-span-4">
              <div className="relative h-80">
                <div className="absolute top-0 left-0">
                  <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={43} />
                </div>
                <div className="absolute top-16 right-8">
                  <AnimatedRoundedRectangle width="xl" height="sm" color="yellow" className="w-32 h-8" animationPreset="drift" animationIndex={44} />
                </div>
                <div className="absolute bottom-16 left-8">
                  <AnimatedRoundedRectangle width="lg" height="xl" color="blue" className="w-16 h-32" animationPreset="gentle" animationIndex={45} />
                </div>
                <div className="absolute bottom-0 right-0">
                  <AnimatedSoftCircle size="xl" color="white" className="w-20 h-20" animationPreset="pulse" animationIndex={46} />
                </div>
              </div>
            </div>

            {/* CTA Content */}
            <div className="col-span-12 lg:col-span-8">
              <div className="space-y-8">
                <div className="space-y-6">
                  <h2 className="text-5xl md:text-6xl font-bold leading-none uppercase tracking-wide">
                    Want to work<br />
                    <span className="text-bauhaus-yellow">with us?</span>
                  </h2>
                  <div className="flex flex-col text-lg">
                    <p>We like projects that are sharp, fast, and meaningful.</p>
                    <p>If that's what you've got, we should talk.</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Link href="/contact" className="bg-bauhaus-red text-white px-8 py-4 rounded-3xl font-bold uppercase tracking-wide hover:bg-red-700 transition-colors">
                    Start Your Project
                  </Link>
                  <AnimatedRoundedRectangle width="lg" height="sm" color="yellow" className="w-16 h-4" animationPreset="gentle" animationIndex={47} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Soft floating elements */}
        <div className="absolute top-20 right-20 opacity-30">
          <AnimatedRoundedRectangle width="md" height="xl" color="yellow" className="w-12 h-40" animationPreset="drift" animationIndex={48} />
        </div>
        <div className="absolute bottom-20 left-20 opacity-40">
          <AnimatedRoundedRectangle width="xl" height="md" color="red" className="w-32 h-12" animationPreset="gentle" animationIndex={49} />
        </div>
        <div className="absolute top-1/3 left-1/4 opacity-50">
          <AnimatedSoftCircle size="sm" color="white" className="w-8 h-8" animationPreset="pulse" animationIndex={50} />
        </div>
        <div className="absolute bottom-1/3 right-1/3 opacity-40">
          <AnimatedRoundedRectangle width="lg" height="sm" color="blue" className="w-16 h-4" animationPreset="gentle" animationIndex={51} />
        </div>
      </section>
    </PageWrapper>
  )
}
