import PageWrapper from '@/components/layout/PageWrapper'
import Circle, { SoftCircle } from '@/components/shapes/Circle'
import Rectangle, { RoundedRectangle } from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'
import { Blob, QuarterCircle } from '@/components/shapes/RoundedShapes'

export default function About() {
  return (
    <PageWrapper>
      {/* Header Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-display font-bold mb-8">Who We Are</h1>
          <p className="text-xl md:text-2xl leading-relaxed text-gray-700">
            Navhaus is a small, sharp team of designers and developers who cut through the noise. 
            No fluff, no filler - just clear thinking and clean execution.
          </p>
        </div>
        
        {/* Decorative shapes */}
        <div className="absolute top-8 left-8 opacity-60">
          <RoundedRectangle width="sm" height="lg" color="blue" />
        </div>
        <div className="absolute top-16 right-12 opacity-60">
          <SoftCircle size="md" color="yellow" />
        </div>
        <div className="absolute bottom-8 right-8 opacity-40">
          <Blob color="red" className="w-16 h-16" />
        </div>
      </section>

      {/* Beliefs Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {/* Belief 1 */}
            <div className="relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6">
              <div className="absolute -top-4 -left-4">
                <SoftCircle size="lg" color="red" />
              </div>
              <h3 className="text-heading font-bold pt-4">Clarity Over Complexity</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We believe good design is invisible. Simplicity scales.
              </p>
            </div>

            {/* Belief 2 */}
            <div className="relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6">
              <div className="absolute -top-4 -right-4">
                <RoundedRectangle width="lg" height="md" color="yellow" />
              </div>
              <h3 className="text-heading font-bold pt-4">Small Team, Big Output</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                You work directly with the people doing the work.
              </p>
            </div>

            {/* Belief 3 */}
            <div className="relative bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-6">
              <div className="absolute -bottom-4 -left-4">
                <Triangle size="lg" color="blue" direction="up" />
              </div>
              <h3 className="text-heading font-bold">Form Follows Function</h3>
              <p className="text-body text-gray-700 leading-relaxed pb-4">
                Every pixel, every line of code has a reason to exist.
              </p>
            </div>
          </div>
        </div>
        
        {/* Side decorative elements */}
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 opacity-40">
          <div className="space-y-4">
            <RoundedRectangle width="sm" height="sm" color="red" />
            <Blob color="blue" className="w-8 h-12" />
            <RoundedRectangle width="sm" height="sm" color="yellow" />
          </div>
        </div>

        <div className="absolute right-8 bottom-8 opacity-40">
          <QuarterCircle color="red" corner="top-left" className="w-24 h-24" />
        </div>
      </section>
    </PageWrapper>
  )
}
