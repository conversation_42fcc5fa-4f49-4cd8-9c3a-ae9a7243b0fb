import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill,
  AnimatedQuarterCircle,
  AnimatedHalfCircle
} from '@/components/shapes/AnimatedShapes'

export default function About() {
  return (
    <PageWrapper>
      {/* What We Are - Hero Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-12 md:py-16 overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12 items-center">
            {/* Content */}
            <div className="lg:col-span-3 space-y-6">
              <h1 className="text-display font-bold mb-6">What We Are</h1>
              <div className="space-y-4 text-lg md:text-xl leading-relaxed text-gray-700">
                <p>
                  Nav<PERSON> is a small, senior team of designers and developers.
                </p>
                <p>
                  We build custom digital experiences. Fast, focused, and built to last.
                </p>
                <p className="font-medium text-bauhaus-black">
                  No fluff. No bloat. Just what matters, made real.
                </p>
              </div>
            </div>

            {/* Geometric Composition */}
            <div className="lg:col-span-2 relative h-72 lg:h-80">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-40">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={1} />
              </div>

              {/* Large shapes */}
              <div className="absolute top-0 right-0 w-28 h-28">
                <AnimatedSoftCircle size="xl" color="red" animationPreset="gentle" animationIndex={2} />
              </div>
              <div className="absolute bottom-0 left-0 w-20 h-36">
                <AnimatedRoundedRectangle width="lg" height="xl" color="blue" animationPreset="flowing" animationIndex={3} />
              </div>

              {/* Medium shapes */}
              <div className="absolute top-1/3 left-1/4 w-18 h-18">
                <AnimatedTriangle size="lg" color="yellow" direction="up" animationPreset="pulse" animationIndex={4} />
              </div>
              <div className="absolute bottom-1/4 right-1/4 w-14 h-6">
                <AnimatedPill color="black" animationPreset="horizontal" animationIndex={5} />
              </div>

              {/* Small accent shapes */}
              <div className="absolute top-2/3 left-1/6 w-10 h-10">
                <AnimatedBlob color="yellow" animationPreset="energetic" animationIndex={6} />
              </div>

              {/* Additional small shapes for density */}
              <div className="absolute top-1/6 left-1/2 w-6 h-6">
                <AnimatedSoftCircle size="sm" color="red" animationPreset="subtle" animationIndex={39} />
              </div>
              <div className="absolute bottom-1/6 right-1/6 w-8 h-4">
                <AnimatedPill color="blue" className="w-8 h-4" animationPreset="horizontal" animationIndex={40} />
              </div>
            </div>
          </div>
        </div>

        {/* Side decorative elements for more visual density */}
        <div className="absolute left-4 top-1/3 opacity-40">
          <div className="space-y-4">
            <AnimatedSoftCircle size="sm" color="yellow" animationPreset="drift" animationIndex={41} />
            <AnimatedPill color="red" className="w-3 h-12" animationPreset="horizontal" animationIndex={42} />
          </div>
        </div>
        <div className="absolute right-4 bottom-1/3 opacity-40">
          <div className="space-y-4">
            <AnimatedTriangle size="sm" color="blue" direction="up" animationPreset="gentle" animationIndex={43} />
            <AnimatedBlob color="yellow" className="w-6 h-6" animationPreset="energetic" animationIndex={44} />
          </div>
        </div>
      </section>

      {/* What We Believe - Split Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-12 md:py-16 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-display font-bold text-center mb-12">What We Believe</h2>

          {/* Belief Cards - Staggered Layout */}
          <div className="space-y-12">
            {/* Belief 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 items-center">
              <div className="lg:col-span-8">
                <div className="relative bg-brand-background text-bauhaus-black p-6 md:p-8 rounded-3xl">
                  <h3 className="text-heading font-bold mb-4">Clarity is everything.</h3>
                  <div className="space-y-3 text-lg leading-relaxed">
                    <p>Good design doesn't scream. It works.</p>
                    <p>Good code doesn't show off. It ships.</p>
                  </div>

                  {/* Decorative element */}
                  <div className="absolute -top-4 -right-4">
                    <AnimatedSoftCircle size="lg" color="red" animationPreset="subtle" animationIndex={7} />
                  </div>
                </div>
              </div>
              <div className="lg:col-span-4 relative h-40 lg:h-48">
                <div className="absolute top-0 right-0 w-20 h-20">
                  <AnimatedQuarterCircle color="yellow" corner="top-right" animationPreset="gentle" animationIndex={8} />
                </div>
                <div className="absolute bottom-0 left-0 w-28 h-14">
                  <AnimatedRoundedRectangle width="xl" height="md" color="red" animationPreset="flowing" animationIndex={9} />
                </div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8">
                  <AnimatedBlob color="blue" className="w-8 h-8" animationPreset="energetic" animationIndex={45} />
                </div>
              </div>
            </div>

            {/* Belief 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 items-center">
              <div className="lg:col-span-4 relative h-40 lg:h-48 order-2 lg:order-1">
                <div className="absolute top-0 left-0 w-18 h-18">
                  <AnimatedTriangle size="lg" color="blue" direction="up" animationPreset="pulse" animationIndex={10} />
                </div>
                <div className="absolute bottom-0 right-0 w-24 h-24">
                  <AnimatedBlob color="yellow" animationPreset="dynamic" animationIndex={11} />
                </div>
                <div className="absolute top-1/3 right-1/3 w-6 h-6">
                  <AnimatedSoftCircle size="sm" color="red" animationPreset="gentle" animationIndex={46} />
                </div>
              </div>
              <div className="lg:col-span-8 order-1 lg:order-2">
                <div className="relative bg-brand-background text-bauhaus-black p-6 md:p-8 rounded-3xl">
                  <h3 className="text-heading font-bold mb-4">Small teams move faster.</h3>
                  <div className="space-y-3 text-lg leading-relaxed">
                    <p>You work directly with the people doing the work.</p>
                    <p>No account managers. No middle layers. No miscommunication.</p>
                  </div>

                  {/* Decorative element */}
                  <div className="absolute -top-4 -left-4">
                    <AnimatedRoundedRectangle width="lg" height="md" color="blue" animationPreset="gentle" animationIndex={12} />
                  </div>
                </div>
              </div>
            </div>

            {/* Belief 3 - Center aligned */}
            <div className="max-w-4xl mx-auto">
              <div className="relative bg-brand-background text-bauhaus-black p-6 md:p-8 rounded-3xl text-center">
                <h3 className="text-heading font-bold mb-4">Function first, aesthetics second.</h3>
                <div className="space-y-3 text-lg leading-relaxed">
                  <p>Beauty is great. Usability is greater.</p>
                  <p>We never choose one at the cost of the other.</p>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 left-1/4">
                  <AnimatedPill color="yellow" animationPreset="horizontal" animationIndex={13} />
                </div>
                <div className="absolute -bottom-4 right-1/4">
                  <AnimatedHalfCircle size="lg" color="red" direction="bottom" animationPreset="flowing" animationIndex={14} />
                </div>
                <div className="absolute top-4 right-4">
                  <AnimatedSoftCircle size="sm" color="blue" animationPreset="subtle" animationIndex={47} />
                </div>
                <div className="absolute bottom-4 left-4">
                  <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="pulse" animationIndex={48} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-16 left-8 opacity-25">
          <AnimatedSoftCircle size="lg" color="red" animationPreset="drift" animationIndex={15} />
        </div>
        <div className="absolute bottom-16 right-8 opacity-25">
          <AnimatedRoundedRectangle width="lg" height="lg" color="blue" animationPreset="subtle" animationIndex={16} />
        </div>
        <div className="absolute top-1/2 left-4 opacity-20">
          <div className="space-y-6">
            <AnimatedPill color="yellow" className="w-4 h-16" animationPreset="horizontal" animationIndex={49} />
            <AnimatedBlob color="red" className="w-8 h-8" animationPreset="energetic" animationIndex={50} />
          </div>
        </div>
        <div className="absolute top-1/3 right-4 opacity-20">
          <div className="space-y-6">
            <AnimatedTriangle size="sm" color="blue" direction="up" animationPreset="gentle" animationIndex={51} />
            <AnimatedSoftCircle size="sm" color="yellow" animationPreset="drift" animationIndex={52} />
          </div>
        </div>
      </section>

      {/* Why We Exist - Vertical Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-12 md:py-16 overflow-hidden">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-display font-bold mb-10">Why We Exist</h2>

          {/* Reasons in vertical stack */}
          <div className="space-y-6 mb-12">
            <div className="relative">
              <p className="text-lg md:text-xl leading-relaxed text-gray-700">
                Because the internet is full of websites that say too much and do too little.
              </p>
              <div className="absolute -left-6 top-0 opacity-60">
                <AnimatedBlob color="red" className="w-5 h-5" animationPreset="pulse" animationIndex={17} />
              </div>
              <div className="absolute -right-6 top-1/2 opacity-40">
                <AnimatedSoftCircle size="sm" color="yellow" animationPreset="drift" animationIndex={53} />
              </div>
            </div>

            <div className="relative">
              <p className="text-lg md:text-xl leading-relaxed text-gray-700">
                Because bloated codebases and unnecessary meetings waste everyone's time.
              </p>
              <div className="absolute -right-6 top-0 opacity-60">
                <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="gentle" animationIndex={18} />
              </div>
              <div className="absolute -left-6 top-1/2 opacity-40">
                <AnimatedPill color="blue" className="w-3 h-8" animationPreset="horizontal" animationIndex={54} />
              </div>
            </div>

            <div className="relative">
              <p className="text-lg md:text-xl leading-relaxed text-gray-700">
                Because you shouldn't have to choose between good design and fast development.
              </p>
              <div className="absolute -left-6 top-0 opacity-60">
                <AnimatedSoftCircle size="sm" color="blue" animationPreset="flowing" animationIndex={19} />
              </div>
              <div className="absolute -right-6 top-1/2 opacity-40">
                <AnimatedBlob color="red" className="w-4 h-4" animationPreset="energetic" animationIndex={55} />
              </div>
            </div>
          </div>

          <div className="relative bg-bauhaus-black text-brand-background p-6 md:p-8 rounded-3xl">
            <p className="text-lg md:text-xl font-medium leading-relaxed">
              We're here to cut through the noise and build the right thing, the right way.
            </p>

            {/* Corner decorations */}
            <div className="absolute -top-3 -left-3">
              <AnimatedQuarterCircle color="red" corner="top-left" className="w-12 h-12" animationPreset="subtle" animationIndex={20} />
            </div>
            <div className="absolute -bottom-3 -right-3">
              <AnimatedQuarterCircle color="yellow" corner="bottom-right" className="w-12 h-12" animationPreset="subtle" animationIndex={21} />
            </div>
            <div className="absolute top-2 right-2">
              <AnimatedSoftCircle size="sm" color="blue" animationPreset="gentle" animationIndex={56} />
            </div>
            <div className="absolute bottom-2 left-2">
              <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="pulse" animationIndex={57} />
            </div>
          </div>
        </div>

        {/* Side decorative elements */}
        <div className="absolute left-0 top-1/4 opacity-35">
          <div className="space-y-6">
            <AnimatedRoundedRectangle width="sm" height="lg" color="blue" animationPreset="drift" animationIndex={22} />
            <AnimatedPill color="yellow" className="w-3 h-12" animationPreset="horizontal" animationIndex={23} />
            <AnimatedSoftCircle size="sm" color="red" animationPreset="gentle" animationIndex={58} />
          </div>
        </div>
        <div className="absolute right-0 bottom-1/4 opacity-35">
          <div className="space-y-6">
            <AnimatedHalfCircle size="md" color="red" direction="right" animationPreset="flowing" animationIndex={24} />
            <AnimatedSoftCircle size="sm" color="blue" animationPreset="gentle" animationIndex={25} />
            <AnimatedBlob color="yellow" className="w-6 h-6" animationPreset="energetic" animationIndex={59} />
          </div>
        </div>
      </section>

      {/* How We Work - Diagonal Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-12 md:py-16 bg-brand-background overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-display font-bold text-center mb-12">How We Work</h2>

          {/* Diagonal grid of work principles */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Step 1 */}
            <div className="relative bg-bauhaus-red text-white p-6 rounded-3xl transform md:rotate-2">
              <div className="text-center">
                <div className="text-3xl font-bold mb-3">01</div>
                <h3 className="text-base font-bold mb-3">We listen first.</h3>
              </div>
              <div className="absolute -top-2 -right-2">
                <AnimatedSoftCircle size="sm" color="yellow" animationPreset="pulse" animationIndex={26} />
              </div>
              <div className="absolute bottom-2 left-2">
                <AnimatedPill color="yellow" className="w-6 h-3" animationPreset="horizontal" animationIndex={60} />
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl transform md:-rotate-1 md:mt-6">
              <div className="text-center">
                <div className="text-3xl font-bold mb-3">02</div>
                <h3 className="text-base font-bold mb-3">We distill ideas into clear, minimal interfaces.</h3>
              </div>
              <div className="absolute -bottom-2 -left-2">
                <AnimatedTriangle size="sm" color="red" direction="up" animationPreset="gentle" animationIndex={27} />
              </div>
              <div className="absolute top-2 right-2">
                <AnimatedBlob color="blue" className="w-5 h-5" animationPreset="energetic" animationIndex={61} />
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative bg-bauhaus-blue text-white p-6 rounded-3xl transform md:rotate-1">
              <div className="text-center">
                <div className="text-3xl font-bold mb-3">03</div>
                <h3 className="text-base font-bold mb-3">We build with clean systems, not hacks or patchwork.</h3>
              </div>
              <div className="absolute -top-2 -left-2">
                <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" animationPreset="flowing" animationIndex={28} />
              </div>
              <div className="absolute bottom-2 right-2">
                <AnimatedSoftCircle size="sm" color="red" animationPreset="gentle" animationIndex={62} />
              </div>
            </div>

            {/* Step 4 */}
            <div className="relative bg-bauhaus-black text-brand-background p-6 rounded-3xl transform md:-rotate-2 md:mt-6">
              <div className="text-center">
                <div className="text-3xl font-bold mb-3">04</div>
                <h3 className="text-base font-bold mb-3">We don't outsource. We don't upsell. We don't hide behind jargon.</h3>
              </div>
              <div className="absolute -bottom-2 -right-2">
                <AnimatedBlob color="red" className="w-6 h-6" animationPreset="energetic" animationIndex={29} />
              </div>
              <div className="absolute top-2 left-2">
                <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="pulse" animationIndex={63} />
              </div>
            </div>
          </div>
        </div>

        {/* Background grid */}
        <div className="absolute inset-0 opacity-25">
          <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={30} />
        </div>

        {/* Additional background elements for density */}
        <div className="absolute top-8 left-8 opacity-15">
          <div className="space-y-4">
            <AnimatedSoftCircle size="sm" color="red" animationPreset="drift" animationIndex={64} />
            <AnimatedPill color="blue" className="w-3 h-8" animationPreset="horizontal" animationIndex={65} />
          </div>
        </div>
        <div className="absolute bottom-8 right-8 opacity-15">
          <div className="space-y-4">
            <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="gentle" animationIndex={66} />
            <AnimatedBlob color="red" className="w-6 h-6" animationPreset="energetic" animationIndex={67} />
          </div>
        </div>
      </section>

      {/* CTA Section - Asymmetric Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-20 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center">
            {/* Large geometric element */}
            <div className="lg:col-span-1 relative h-56 lg:h-64">
              <div className="absolute top-0 left-0 w-28 h-28">
                <AnimatedSoftCircle size="xl" color="red" animationPreset="gentle" animationIndex={31} />
              </div>
              <div className="absolute bottom-0 right-0 w-32 h-16">
                <AnimatedRoundedRectangle width="xl" height="lg" color="yellow" animationPreset="flowing" animationIndex={32} />
              </div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
                <AnimatedTriangle size="lg" color="blue" direction="up" animationPreset="pulse" animationIndex={33} />
              </div>
              <div className="absolute top-1/4 right-1/4 w-12 h-6">
                <AnimatedPill color="red" animationPreset="horizontal" animationIndex={34} />
              </div>
              <div className="absolute bottom-1/4 left-1/4 w-8 h-8">
                <AnimatedBlob color="yellow" className="w-8 h-8" animationPreset="energetic" animationIndex={68} />
              </div>
              <div className="absolute top-1/6 right-1/6 w-6 h-6">
                <AnimatedSoftCircle size="sm" color="blue" animationPreset="gentle" animationIndex={69} />
              </div>
            </div>

            {/* CTA Content */}
            <div className="lg:col-span-2 space-y-6">
              <div className="space-y-4">
                <h2 className="text-display font-bold">Want to work with us?</h2>
                <div className="space-y-3 text-lg md:text-xl leading-relaxed">
                  <p>We like projects that are sharp, fast, and meaningful.</p>
                  <p>If that's what you've got, we should talk.</p>
                </div>
              </div>

              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-8 right-8 opacity-15">
          <AnimatedHalfCircle size="lg" color="yellow" direction="top" animationPreset="drift" animationIndex={35} />
        </div>
        <div className="absolute bottom-8 left-8 opacity-15">
          <AnimatedQuarterCircle color="blue" corner="bottom-left" className="w-24 h-24" animationPreset="subtle" animationIndex={36} />
        </div>

        {/* Floating accent shapes */}
        <div className="absolute top-1/3 left-1/6 opacity-25">
          <AnimatedBlob color="red" className="w-12 h-12" animationPreset="energetic" animationIndex={37} />
        </div>
        <div className="absolute bottom-1/3 right-1/4 opacity-25">
          <AnimatedSoftCircle size="md" color="yellow" animationPreset="gentle" animationIndex={38} />
        </div>
        <div className="absolute top-1/6 left-1/3 opacity-20">
          <div className="space-y-3">
            <AnimatedTriangle size="sm" color="red" direction="up" animationPreset="pulse" animationIndex={70} />
            <AnimatedPill color="blue" className="w-4 h-8" animationPreset="horizontal" animationIndex={71} />
          </div>
        </div>
        <div className="absolute bottom-1/6 right-1/6 opacity-20">
          <div className="space-y-3">
            <AnimatedSoftCircle size="sm" color="yellow" animationPreset="drift" animationIndex={72} />
            <AnimatedBlob color="red" className="w-6 h-6" animationPreset="energetic" animationIndex={73} />
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
