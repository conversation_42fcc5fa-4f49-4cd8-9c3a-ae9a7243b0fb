import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill,
  AnimatedQuarterCircle,
  AnimatedHalfCircle
} from '@/components/shapes/AnimatedShapes'

export default function About() {
  return (
    <PageWrapper>
      {/* What We Are - Hero Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 lg:gap-16 items-center">
            {/* Content */}
            <div className="lg:col-span-3 space-y-8">
              <h1 className="text-display font-bold mb-8">What We Are</h1>
              <div className="space-y-6 text-lg md:text-xl leading-relaxed text-gray-700">
                <p>
                  Nav<PERSON> is a small, senior team of designers and developers.
                </p>
                <p>
                  We build custom digital experiences. Fast, focused, and built to last.
                </p>
                <p className="font-medium text-bauhaus-black">
                  No fluff. No bloat. Just what matters, made real.
                </p>
              </div>
            </div>

            {/* Geometric Composition */}
            <div className="lg:col-span-2 relative h-80 lg:h-96">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-30">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={1} />
              </div>

              {/* Large shapes */}
              <div className="absolute top-0 right-0 w-32 h-32">
                <AnimatedSoftCircle size="xl" color="red" animationPreset="gentle" animationIndex={2} />
              </div>
              <div className="absolute bottom-0 left-0 w-24 h-40">
                <AnimatedRoundedRectangle width="lg" height="xl" color="blue" animationPreset="flowing" animationIndex={3} />
              </div>

              {/* Medium shapes */}
              <div className="absolute top-1/3 left-1/4 w-20 h-20">
                <AnimatedTriangle size="lg" color="yellow" direction="up" animationPreset="pulse" animationIndex={4} />
              </div>
              <div className="absolute bottom-1/4 right-1/4 w-16 h-8">
                <AnimatedPill color="black" animationPreset="horizontal" animationIndex={5} />
              </div>

              {/* Small accent shapes */}
              <div className="absolute top-2/3 left-1/6 w-12 h-12">
                <AnimatedBlob color="yellow" animationPreset="energetic" animationIndex={6} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What We Believe - Split Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">What We Believe</h2>

          {/* Belief Cards - Staggered Layout */}
          <div className="space-y-16">
            {/* Belief 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
              <div className="lg:col-span-7">
                <div className="relative bg-brand-background text-bauhaus-black p-8 md:p-12 rounded-3xl">
                  <h3 className="text-heading font-bold mb-6">Clarity is everything.</h3>
                  <div className="space-y-4 text-lg leading-relaxed">
                    <p>Good design doesn't scream. It works.</p>
                    <p>Good code doesn't show off. It ships.</p>
                  </div>

                  {/* Decorative element */}
                  <div className="absolute -top-6 -right-6">
                    <AnimatedSoftCircle size="lg" color="red" animationPreset="subtle" animationIndex={7} />
                  </div>
                </div>
              </div>
              <div className="lg:col-span-5 relative h-48 lg:h-64">
                <div className="absolute top-0 right-0 w-24 h-24">
                  <AnimatedQuarterCircle color="yellow" corner="top-right" animationPreset="gentle" animationIndex={8} />
                </div>
                <div className="absolute bottom-0 left-0 w-32 h-16">
                  <AnimatedRoundedRectangle width="xl" height="md" color="red" animationPreset="flowing" animationIndex={9} />
                </div>
              </div>
            </div>

            {/* Belief 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
              <div className="lg:col-span-5 relative h-48 lg:h-64 order-2 lg:order-1">
                <div className="absolute top-0 left-0 w-20 h-20">
                  <AnimatedTriangle size="lg" color="blue" direction="up" animationPreset="pulse" animationIndex={10} />
                </div>
                <div className="absolute bottom-0 right-0 w-28 h-28">
                  <AnimatedBlob color="yellow" animationPreset="dynamic" animationIndex={11} />
                </div>
              </div>
              <div className="lg:col-span-7 order-1 lg:order-2">
                <div className="relative bg-brand-background text-bauhaus-black p-8 md:p-12 rounded-3xl">
                  <h3 className="text-heading font-bold mb-6">Small teams move faster.</h3>
                  <div className="space-y-4 text-lg leading-relaxed">
                    <p>You work directly with the people doing the work.</p>
                    <p>No account managers. No middle layers. No miscommunication.</p>
                  </div>

                  {/* Decorative element */}
                  <div className="absolute -top-6 -left-6">
                    <AnimatedRoundedRectangle width="lg" height="md" color="blue" animationPreset="gentle" animationIndex={12} />
                  </div>
                </div>
              </div>
            </div>

            {/* Belief 3 - Center aligned */}
            <div className="max-w-4xl mx-auto">
              <div className="relative bg-brand-background text-bauhaus-black p-8 md:p-12 rounded-3xl text-center">
                <h3 className="text-heading font-bold mb-6">Function first, aesthetics second.</h3>
                <div className="space-y-4 text-lg leading-relaxed">
                  <p>Beauty is great. Usability is greater.</p>
                  <p>We never choose one at the cost of the other.</p>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-6 left-1/4">
                  <AnimatedPill color="yellow" animationPreset="horizontal" animationIndex={13} />
                </div>
                <div className="absolute -bottom-6 right-1/4">
                  <AnimatedHalfCircle size="lg" color="red" direction="bottom" animationPreset="flowing" animationIndex={14} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-20 left-10 opacity-20">
          <AnimatedSoftCircle size="xl" color="red" animationPreset="drift" animationIndex={15} />
        </div>
        <div className="absolute bottom-20 right-10 opacity-20">
          <AnimatedRoundedRectangle width="xl" height="lg" color="blue" animationPreset="subtle" animationIndex={16} />
        </div>
      </section>

      {/* Why We Exist - Vertical Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-display font-bold mb-12">Why We Exist</h2>

          {/* Reasons in vertical stack */}
          <div className="space-y-8 mb-16">
            <div className="relative">
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700">
                Because the internet is full of websites that say too much and do too little.
              </p>
              <div className="absolute -left-8 top-0 opacity-60">
                <AnimatedBlob color="red" className="w-6 h-6" animationPreset="pulse" animationIndex={17} />
              </div>
            </div>

            <div className="relative">
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700">
                Because bloated codebases and unnecessary meetings waste everyone's time.
              </p>
              <div className="absolute -right-8 top-0 opacity-60">
                <AnimatedTriangle size="sm" color="yellow" direction="up" animationPreset="gentle" animationIndex={18} />
              </div>
            </div>

            <div className="relative">
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700">
                Because you shouldn't have to choose between good design and fast development.
              </p>
              <div className="absolute -left-8 top-0 opacity-60">
                <AnimatedSoftCircle size="sm" color="blue" animationPreset="flowing" animationIndex={19} />
              </div>
            </div>
          </div>

          <div className="relative bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl">
            <p className="text-xl md:text-2xl font-medium leading-relaxed">
              We're here to cut through the noise and build the right thing, the right way.
            </p>

            {/* Corner decorations */}
            <div className="absolute -top-4 -left-4">
              <AnimatedQuarterCircle color="red" corner="top-left" className="w-16 h-16" animationPreset="subtle" animationIndex={20} />
            </div>
            <div className="absolute -bottom-4 -right-4">
              <AnimatedQuarterCircle color="yellow" corner="bottom-right" className="w-16 h-16" animationPreset="subtle" animationIndex={21} />
            </div>
          </div>
        </div>

        {/* Side decorative elements */}
        <div className="absolute left-0 top-1/4 opacity-30">
          <div className="space-y-8">
            <AnimatedRoundedRectangle width="sm" height="lg" color="blue" animationPreset="drift" animationIndex={22} />
            <AnimatedPill color="yellow" className="w-4 h-16" animationPreset="horizontal" animationIndex={23} />
          </div>
        </div>
        <div className="absolute right-0 bottom-1/4 opacity-30">
          <div className="space-y-8">
            <AnimatedHalfCircle size="lg" color="red" direction="right" animationPreset="flowing" animationIndex={24} />
            <AnimatedSoftCircle size="md" color="blue" animationPreset="gentle" animationIndex={25} />
          </div>
        </div>
      </section>

      {/* How We Work - Diagonal Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">How We Work</h2>

          {/* Diagonal grid of work principles */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Step 1 */}
            <div className="relative bg-bauhaus-red text-white p-8 rounded-3xl transform md:rotate-2">
              <div className="text-center">
                <div className="text-4xl font-bold mb-4">01</div>
                <h3 className="text-lg font-bold mb-4">We listen first.</h3>
              </div>
              <div className="absolute -top-3 -right-3">
                <AnimatedSoftCircle size="sm" color="yellow" animationPreset="pulse" animationIndex={26} />
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative bg-bauhaus-yellow text-bauhaus-black p-8 rounded-3xl transform md:-rotate-1 md:mt-8">
              <div className="text-center">
                <div className="text-4xl font-bold mb-4">02</div>
                <h3 className="text-lg font-bold mb-4">We distill ideas into clear, minimal interfaces.</h3>
              </div>
              <div className="absolute -bottom-3 -left-3">
                <AnimatedTriangle size="sm" color="red" direction="up" animationPreset="gentle" animationIndex={27} />
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative bg-bauhaus-blue text-white p-8 rounded-3xl transform md:rotate-1">
              <div className="text-center">
                <div className="text-4xl font-bold mb-4">03</div>
                <h3 className="text-lg font-bold mb-4">We build with clean systems, not hacks or patchwork.</h3>
              </div>
              <div className="absolute -top-3 -left-3">
                <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" animationPreset="flowing" animationIndex={28} />
              </div>
            </div>

            {/* Step 4 */}
            <div className="relative bg-bauhaus-black text-brand-background p-8 rounded-3xl transform md:-rotate-2 md:mt-8">
              <div className="text-center">
                <div className="text-4xl font-bold mb-4">04</div>
                <h3 className="text-lg font-bold mb-4">We don't outsource. We don't upsell. We don't hide behind jargon.</h3>
              </div>
              <div className="absolute -bottom-3 -right-3">
                <AnimatedBlob color="red" className="w-8 h-8" animationPreset="energetic" animationIndex={29} />
              </div>
            </div>
          </div>
        </div>

        {/* Background grid */}
        <div className="absolute inset-0 opacity-20">
          <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="drift" animationIndex={30} />
        </div>
      </section>

      {/* CTA Section - Asymmetric Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 lg:gap-16 items-center">
            {/* Large geometric element */}
            <div className="lg:col-span-1 relative h-64 lg:h-80">
              <div className="absolute top-0 left-0 w-32 h-32">
                <AnimatedSoftCircle size="xl" color="red" animationPreset="gentle" animationIndex={31} />
              </div>
              <div className="absolute bottom-0 right-0 w-40 h-20">
                <AnimatedRoundedRectangle width="xl" height="lg" color="yellow" animationPreset="flowing" animationIndex={32} />
              </div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24">
                <AnimatedTriangle size="xl" color="blue" direction="up" animationPreset="pulse" animationIndex={33} />
              </div>
              <div className="absolute top-1/4 right-1/4 w-16 h-8">
                <AnimatedPill color="red" animationPreset="horizontal" animationIndex={34} />
              </div>
            </div>

            {/* CTA Content */}
            <div className="lg:col-span-2 space-y-8">
              <div className="space-y-6">
                <h2 className="text-display font-bold">Want to work with us?</h2>
                <div className="space-y-4 text-xl leading-relaxed">
                  <p>We like projects that are sharp, fast, and meaningful.</p>
                  <p>If that's what you've got, we should talk.</p>
                </div>
              </div>

              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-10 right-10 opacity-10">
          <AnimatedHalfCircle size="xl" color="yellow" direction="top" animationPreset="drift" animationIndex={35} />
        </div>
        <div className="absolute bottom-10 left-10 opacity-10">
          <AnimatedQuarterCircle color="blue" corner="bottom-left" className="w-32 h-32" animationPreset="subtle" animationIndex={36} />
        </div>

        {/* Floating accent shapes */}
        <div className="absolute top-1/3 left-1/4 opacity-20">
          <AnimatedBlob color="red" className="w-16 h-16" animationPreset="energetic" animationIndex={37} />
        </div>
        <div className="absolute bottom-1/3 right-1/3 opacity-20">
          <AnimatedSoftCircle size="lg" color="yellow" animationPreset="gentle" animationIndex={38} />
        </div>
      </section>
    </PageWrapper>
  )
}
