import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'

export default function About() {
  return (
    <PageWrapper>
      {/* What We Are - Asymmetric Swiss Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Swiss-style asymmetric grid */}
          <div className="grid grid-cols-12 gap-6">
            {/* Large typography block */}
            <div className="col-span-12 lg:col-span-8">
              <div className="relative">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-bauhaus-red"></div>
                <h1 className="text-6xl md:text-7xl lg:text-8xl font-black leading-none mb-8 relative z-10">
                  WHAT<br />
                  WE ARE
                </h1>
              </div>
            </div>

            {/* Geometric accent */}
            <div className="col-span-12 lg:col-span-4 flex items-start justify-end">
              <div className="w-32 h-32 bg-bauhaus-yellow transform rotate-45"></div>
            </div>

            {/* Content blocks in asymmetric layout */}
            <div className="col-span-12 lg:col-span-5">
              <div className="bg-bauhaus-black text-brand-background p-8 relative">
                <div className="absolute top-0 right-0 w-4 h-full bg-bauhaus-red"></div>
                <p className="text-lg font-medium leading-tight">
                  Navhaus is a small, senior team of designers and developers.
                </p>
              </div>
            </div>

            <div className="col-span-12 lg:col-span-2">
              <div className="h-full bg-bauhaus-blue"></div>
            </div>

            <div className="col-span-12 lg:col-span-5">
              <div className="space-y-6">
                <div className="bg-brand-background border-4 border-bauhaus-black p-6">
                  <p className="text-lg leading-tight">
                    We build custom digital experiences. Fast, focused, and built to last.
                  </p>
                </div>
                <div className="bg-bauhaus-yellow text-bauhaus-black p-6 font-bold text-lg">
                  No fluff. No bloat. Just what matters, made real.
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bauhaus geometric elements */}
        <div className="absolute top-20 right-20 w-8 h-40 bg-bauhaus-blue opacity-60"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 border-4 border-bauhaus-red opacity-40"></div>
      </section>

      {/* What We Believe - Bauhaus Modular Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Swiss typography header */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-6">
              <h2 className="text-5xl md:text-6xl font-black leading-none">
                WHAT WE<br />
                <span className="text-bauhaus-red">BELIEVE</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-6 flex items-end">
              <div className="w-full h-4 bg-bauhaus-black"></div>
            </div>
          </div>

          {/* Modular belief blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Belief 1 - Large block */}
            <div className="col-span-12 lg:col-span-8">
              <div className="bg-bauhaus-black text-brand-background p-8 md:p-12 relative">
                <div className="absolute top-0 left-0 w-full h-2 bg-bauhaus-red"></div>
                <h3 className="text-2xl md:text-3xl font-black mb-6 uppercase tracking-wide">
                  Clarity is everything.
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg">
                  <p>Good design doesn't scream. It works.</p>
                  <p>Good code doesn't show off. It ships.</p>
                </div>
              </div>
            </div>

            {/* Yellow accent block */}
            <div className="col-span-12 lg:col-span-4">
              <div className="h-full bg-bauhaus-yellow flex items-center justify-center">
                <div className="text-6xl font-black text-bauhaus-black">01</div>
              </div>
            </div>

            {/* Blue accent block */}
            <div className="col-span-12 lg:col-span-3">
              <div className="h-full bg-bauhaus-blue flex items-center justify-center">
                <div className="text-4xl font-black text-white">02</div>
              </div>
            </div>

            {/* Belief 2 - Medium block */}
            <div className="col-span-12 lg:col-span-9">
              <div className="bg-white border-4 border-bauhaus-black p-8 relative">
                <div className="absolute top-0 right-0 w-2 h-full bg-bauhaus-blue"></div>
                <h3 className="text-2xl md:text-3xl font-black mb-6 uppercase tracking-wide">
                  Small teams move faster.
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg">
                  <p>You work directly with the people doing the work.</p>
                  <p>No account managers. No middle layers. No miscommunication.</p>
                </div>
              </div>
            </div>

            {/* Belief 3 - Full width with split */}
            <div className="col-span-12">
              <div className="grid grid-cols-12 gap-0">
                <div className="col-span-12 lg:col-span-2 bg-bauhaus-red flex items-center justify-center">
                  <div className="text-4xl font-black text-white">03</div>
                </div>
                <div className="col-span-12 lg:col-span-10 bg-bauhaus-yellow text-bauhaus-black p-8 md:p-12">
                  <h3 className="text-2xl md:text-3xl font-black mb-6 uppercase tracking-wide">
                    Function first, aesthetics second.
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg">
                    <p>Beauty is great. Usability is greater.</p>
                    <p>We never choose one at the cost of the other.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Geometric accent elements */}
        <div className="absolute top-20 right-20 w-16 h-80 bg-bauhaus-red opacity-30"></div>
        <div className="absolute bottom-20 left-20 w-32 h-32 border-8 border-bauhaus-blue opacity-40"></div>
      </section>

      {/* Why We Exist - Swiss Poster Style */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Large typographic treatment */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-8">
              <h2 className="text-6xl md:text-7xl lg:text-8xl font-black leading-none">
                WHY WE<br />
                <span className="text-bauhaus-yellow">EXIST</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-4 flex items-end justify-end">
              <div className="w-24 h-24 bg-bauhaus-red"></div>
            </div>
          </div>

          {/* Swiss-style reason blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Reason 1 */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-bauhaus-red text-white p-8 h-full flex flex-col justify-between">
                <div className="text-8xl font-black opacity-20">01</div>
                <p className="text-lg leading-tight">
                  Because the internet is full of websites that say too much and do too little.
                </p>
              </div>
            </div>

            {/* Reason 2 */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-brand-background text-bauhaus-black border-4 border-bauhaus-yellow p-8 h-full flex flex-col justify-between">
                <div className="text-8xl font-black text-bauhaus-yellow opacity-50">02</div>
                <p className="text-lg leading-tight">
                  Because bloated codebases and unnecessary meetings waste everyone's time.
                </p>
              </div>
            </div>

            {/* Reason 3 */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-bauhaus-blue text-white p-8 h-full flex flex-col justify-between">
                <div className="text-8xl font-black opacity-20">03</div>
                <p className="text-lg leading-tight">
                  Because you shouldn't have to choose between good design and fast development.
                </p>
              </div>
            </div>

            {/* Full-width conclusion */}
            <div className="col-span-12 mt-8">
              <div className="bg-bauhaus-yellow text-bauhaus-black p-12 text-center relative">
                <div className="absolute top-0 left-0 w-full h-4 bg-bauhaus-black"></div>
                <p className="text-2xl md:text-3xl font-black uppercase tracking-wide">
                  We're here to cut through the noise and build the right thing, the right way.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Geometric accent lines */}
        <div className="absolute left-0 top-1/3 w-8 h-40 bg-bauhaus-yellow opacity-60"></div>
        <div className="absolute right-0 bottom-1/3 w-12 h-60 bg-bauhaus-red opacity-40"></div>
      </section>

      {/* How We Work - Constructivist Grid */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Constructivist header */}
          <div className="grid grid-cols-12 gap-6 mb-16">
            <div className="col-span-12 lg:col-span-6">
              <h2 className="text-5xl md:text-6xl font-black leading-none">
                HOW WE<br />
                <span className="text-bauhaus-blue">WORK</span>
              </h2>
            </div>
            <div className="col-span-12 lg:col-span-6 flex items-center justify-end">
              <div className="w-32 h-8 bg-bauhaus-black"></div>
            </div>
          </div>

          {/* Constructivist process blocks */}
          <div className="grid grid-cols-12 gap-6">
            {/* Step 1 - Large block */}
            <div className="col-span-12 lg:col-span-6">
              <div className="bg-bauhaus-black text-brand-background p-8 md:p-12 relative">
                <div className="absolute top-0 left-0 w-16 h-16 bg-bauhaus-red"></div>
                <div className="text-right">
                  <div className="text-6xl font-black text-bauhaus-red mb-4">01</div>
                  <h3 className="text-xl md:text-2xl font-black uppercase tracking-wide mb-4">
                    We listen first.
                  </h3>
                </div>
              </div>
            </div>

            {/* Step 2 - Vertical block */}
            <div className="col-span-12 lg:col-span-3">
              <div className="bg-bauhaus-yellow text-bauhaus-black p-8 h-full flex flex-col justify-between">
                <div className="text-6xl font-black">02</div>
                <h3 className="text-lg font-black uppercase tracking-wide">
                  We distill ideas into clear, minimal interfaces.
                </h3>
              </div>
            </div>

            {/* Step 3 - Square block */}
            <div className="col-span-12 lg:col-span-3">
              <div className="bg-bauhaus-blue text-white p-8 aspect-square flex flex-col justify-between">
                <div className="text-6xl font-black">03</div>
                <h3 className="text-lg font-black uppercase tracking-wide">
                  We build with clean systems, not hacks.
                </h3>
              </div>
            </div>

            {/* Step 4 - Wide block */}
            <div className="col-span-12">
              <div className="grid grid-cols-12 gap-0">
                <div className="col-span-12 lg:col-span-2 bg-bauhaus-red flex items-center justify-center">
                  <div className="text-6xl font-black text-white">04</div>
                </div>
                <div className="col-span-12 lg:col-span-10 bg-white border-4 border-bauhaus-black p-8 md:p-12 flex items-center">
                  <h3 className="text-xl md:text-2xl font-black uppercase tracking-wide">
                    We don't outsource. We don't upsell. We don't hide behind jargon.
                  </h3>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Geometric accent elements */}
        <div className="absolute top-20 left-20 w-4 h-60 bg-bauhaus-red opacity-40"></div>
        <div className="absolute bottom-20 right-20 w-40 h-4 bg-bauhaus-blue opacity-60"></div>
      </section>

      {/* CTA Section - Suprematist Layout */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-12 gap-6 items-center">
            {/* Suprematist geometric composition */}
            <div className="col-span-12 lg:col-span-4">
              <div className="relative h-80">
                <div className="absolute top-0 left-0 w-24 h-24 bg-bauhaus-red transform rotate-45"></div>
                <div className="absolute top-16 right-8 w-32 h-8 bg-bauhaus-yellow"></div>
                <div className="absolute bottom-16 left-8 w-16 h-32 bg-bauhaus-blue"></div>
                <div className="absolute bottom-0 right-0 w-20 h-20 border-4 border-brand-background"></div>
              </div>
            </div>

            {/* CTA Content */}
            <div className="col-span-12 lg:col-span-8">
              <div className="space-y-8">
                <div className="space-y-6">
                  <h2 className="text-5xl md:text-6xl font-black leading-none uppercase tracking-wide">
                    Want to work<br />
                    <span className="text-bauhaus-yellow">with us?</span>
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg">
                    <p>We like projects that are sharp, fast, and meaningful.</p>
                    <p>If that's what you've got, we should talk.</p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Link href="/contact" className="bg-bauhaus-red text-white px-8 py-4 font-black uppercase tracking-wide hover:bg-red-700 transition-colors">
                    Start Your Project
                  </Link>
                  <div className="w-16 h-4 bg-bauhaus-yellow"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Suprematist floating elements */}
        <div className="absolute top-20 right-20 w-12 h-40 bg-bauhaus-yellow opacity-30"></div>
        <div className="absolute bottom-20 left-20 w-32 h-12 bg-bauhaus-red opacity-40"></div>
        <div className="absolute top-1/3 left-1/4 w-8 h-8 bg-brand-background transform rotate-45 opacity-60"></div>
        <div className="absolute bottom-1/3 right-1/3 w-16 h-4 bg-bauhaus-blue opacity-50"></div>
      </section>
    </PageWrapper>
  )
}
